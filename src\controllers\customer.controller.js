const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { sendSuccess, sendError } = require('../utils/ApiResponse');
const GlobalService = require('../services/GlobalService');
const pick = require('../utils/pick');
const { userService } = require('../services');
const {User , Product , Order } = require('../models');
const {   notifyCustomerAndProviders } = require('./notification.controller');
const { mongoose } = require('../config/config');
const { convertCoordinatesToLocation } = require('../utils/coordinateHelper');

const modelName = 'Customer';
const resourceName = 'Customer';
const resourcesName = 'Customers';
const aggregation = [
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'userDetails',
      },
    },
    {
      $project: {
        email: { $arrayElemAt: ['$userDetails.email', 0] },
        user: 1,
        username: 1,
        emailPermission: 1,
        photo: 1,
        bio: 1,
        notificationPermission: 1,
        latitude: 1,
        longitude: 1,
        address: 1,
        createdAt: 1,
        updatedAt: 1,
      }
    }
];

const create = catchAsync(async (req, res) => {
  // Convert coordinates to location using helper function
  convertCoordinatesToLocation(req.body);

  const user = await userService.createUser(pick(req.body, ['email', 'password']));
  req.body['user'] = user._id;
  const customer = await GlobalService.create(modelName, req.body);
  sendSuccess(res, `${resourceName} created successfully!`, httpStatus.CREATED, customer);
});

// Get all customers
const index = catchAsync(async (req, res) => {
  const options = req.queryOptions || pick(req.query, ['sortBy', 'limit', 'page']);
  const searchFilter = req.searchFilter;
  const customers = await GlobalService.getAll(modelName, options, aggregation, searchFilter);
  sendSuccess(res, `${resourcesName} fetched successfully!`, httpStatus.OK, customers);
});

const users = catchAsync(async (req, res) => {
  const options = { ...req.query };
  const users = await GlobalService.getAll('User', options);
  sendSuccess(res, 'Users fetched successfully!', httpStatus.OK, users);
});

const view = catchAsync(async (req, res) => {
  const customer = await GlobalService.getById(modelName, req.params.customer, aggregation);
  if (!customer) {
    return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);
  }
  sendSuccess(res, `${resourceName} fetched successfully!`, httpStatus.OK, customer);
});

const update = catchAsync(async (req, res) => {
  // Convert coordinates to location using helper function
  convertCoordinatesToLocation(req.body);

  if (req.body.email){
      const customer = await GlobalService.getById(modelName, req.body.customer);
      await GlobalService.updateById("User", customer.user, pick(req.body, ['email']));
    }
  const customer = await GlobalService.updateById(modelName, req.body.customer, req.body, aggregation);
  sendSuccess(res, `${resourceName} updated successfully!`, httpStatus.OK, customer);
});

const softDelete = catchAsync(async (req, res) => {
  const customer = await GlobalService.getById(modelName, req.params.customer, aggregation);
  if (!customer) return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);
  await GlobalService.softDeleteById("User", ['email'], customer.user);
  await GlobalService.softDeleteById(modelName, ['username'], req.params.customer);
  sendSuccess(res, `${resourceName} deleted successfully!`, httpStatus.NO_CONTENT);
});

const username = catchAsync(async (req, res) => {
  req.body['user'] = req.user._id;
  let customer = await GlobalService.create(modelName, req.body);
  customer = await GlobalService.getById(modelName, customer._id, aggregation);
  sendSuccess(res, `${resourceName} updated successfully!`, httpStatus.OK, customer) ;
});

/* The `updateCustomer` function is updating a customer's information in the database. It first calls
the `GlobalService.updateById` method to update the customer's details based on the provided request
body (`req.body`) and aggregation settings. */
const updateCustomer = catchAsync(async (req, res) => {
  if (req.files && req.files.photo && req.files.photo[0]) {
    const uploadedFile = req.files.photo[0];
    req.body.photo = uploadedFile.path.replace(/\\/g, '/').replace(/^.*\/src\//, '');
  }
  const customerId = req.customer ? req.customer._id : req.user._id;
  let customer = await GlobalService.getById(modelName, customerId);
  if (!customer) {
    const customers = await GlobalService.getAllByFilter(modelName, { user: req.user._id });
    if (customers.length === 0) {
      return sendError(res, 'Customer profile not found', httpStatus.NOT_FOUND);
    }
    customer = customers[0];
  }
  const updateData = {};
  if (req.body.photo) {
    updateData.photo = req.body.photo;
  }
  if (req.body.bio !== undefined) {
    updateData.bio = req.body.bio;
  }
  if (Object.keys(updateData).length === 0) {
    return sendError(res, 'No data provided to update', httpStatus.BAD_REQUEST);
  }
  const updatedCustomer = await GlobalService.updateById(modelName, customer._id, updateData, aggregation);
  if (req.body.email) {
    await GlobalService.updateById("User", req.user._id, pick(req.body, ['email']));
  }
  sendSuccess(res, `${resourceName} profile updated successfully!`, httpStatus.OK, updatedCustomer);
});

const updateCustomerAddress = catchAsync(async (req, res) => {
  try {
    const { location } = req.body;
    if (!location || !location.coordinates) {
      return res.status(400).json({
        message: 'Location with coordinates array is required',
        example: {
          location: {
            type: "Point",
            coordinates: [77.5946, 12.9716] // [longitude, latitude]
          }
        }
      });
    }
    const [longitude, latitude] = location.coordinates;
    if (
      !Array.isArray(location.coordinates) ||
      location.coordinates.length !== 2 ||
      typeof longitude !== 'number' ||
      typeof latitude !== 'number'
    ) {
      return res.status(400).json({
        message: 'Invalid coordinates format. Expected [longitude, latitude] numbers'
      });
    }

    // 3. Update with validation
    const updatedCustomer = await GlobalService.updateById(
      modelName,
      req.customer._id,
      {
        location: {
          type: "Point",
          coordinates: [
            parseFloat(longitude),
            parseFloat(latitude)
          ]
        }
      },
      aggregation,
      { runValidators: true } // ENABLE VALIDATORS
    );

    // 4. Trigger notifications
    const { totalNotifications } = await notifyCustomerAndProviders(req.customer._id);

    return res.status(200).json({
      message: `Address updated successfully. Notified ${totalNotifications} matches!`,
      customer: updatedCustomer,
    });
  } catch (error) {
    console.error('Address update error:', error);
    throw error;
  }
});

const getNearbyProviders = catchAsync(async (req, res) => {
  const customerId = req.user._id;

  // Find the customer with their location
  const customer = await GlobalService.getAllByFilter('Customer', { user: customerId });
  if (!customer || customer.length === 0) {
    return sendError(res, 'Customer profile not found', httpStatus.NOT_FOUND);
  }

  const customerProfile = customer[0];

  // Check if customer has location data
  if (
    !customerProfile.location ||
    !customerProfile.location.coordinates ||
    customerProfile.location.coordinates.length !== 2
  ) {
    return sendError(res, 'Customer location is required to find nearby providers. Please update your location in profile.', httpStatus.BAD_REQUEST);
  }

  console.log('Customer location:', customerProfile.location.coordinates);

  // Use MongoDB geospatial query to find nearby providers within 100km
  const { Provider, User } = require('../models');

  let nearbyProviders = [];
  try {
    // Find nearby providers using latitude/longitude fields
    // Since providers store lat/lng as separate fields, we'll use a different approach
    const providersWithDistance = await Provider.find({
      isApproved: 'approved',
      latitude: { $exists: true },
      longitude: { $exists: true }
    }).lean();

    if (providersWithDistance.length === 0) {
      return sendSuccess(res, 'No approved providers found within 100km radius', httpStatus.OK, []);
    }

    // Calculate distance manually and get additional data
    const providerIds = providersWithDistance.map(p => p._id);

    // Get provider details with user info and ratings in a single aggregation
    nearbyProviders = await Provider.aggregate([
      {
        $match: {
          _id: { $in: providerIds }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'user',
          foreignField: '_id',
          as: 'userDetails',
          pipeline: [{ $project: { email: 1 } }]
        }
      },
      {
        $unwind: {
          path: '$userDetails',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: 'reviews',
          let: { providerId: '$_id' },
          pipeline: [
            {
              $lookup: {
                from: 'orders',
                localField: 'order_id',
                foreignField: '_id',
                as: 'order'
              }
            },
            {
              $unwind: '$order'
            },
            {
              $match: {
                $expr: { $eq: ['$order.provider_id', '$$providerId'] }
              }
            },
            {
              $group: {
                _id: null,
                averageRating: { $avg: '$rating' },
                totalReviews: { $sum: 1 }
              }
            }
          ],
          as: 'ratingData'
        }
      },
      {
        $addFields: {
          // Calculate distance using Haversine formula with separate lat/lng fields
          distance: {
            $multiply: [
              {
                $acos: {
                  $add: [
                    {
                      $multiply: [
                        { $sin: { $degreesToRadians: { $toDouble: '$latitude' } } },
                        { $sin: { $degreesToRadians: customerProfile.location.coordinates[1] } }
                      ]
                    },
                    {
                      $multiply: [
                        { $cos: { $degreesToRadians: { $toDouble: '$latitude' } } },
                        { $cos: { $degreesToRadians: customerProfile.location.coordinates[1] } },
                        { $cos: {
                          $degreesToRadians: {
                            $subtract: [
                              { $toDouble: '$longitude' },
                              customerProfile.location.coordinates[0]
                            ]
                          }
                        }}
                      ]
                    }
                  ]
                }
              },
              6371 // Earth's radius in kilometers
            ]
          }
        }
      },
      {
        $match: {
          distance: { $lte: 100 } // Filter providers within 100km
        }
      },
      {
        $project: {
          _id: 1,
          name: 1,
          description: 1,
          photoId: 1,
          email: '$userDetails.email',
          street: 1,
          city: 1,
          state: 1,
          country: 1,
          zipCode: 1,
          latitude: 1,
          longitude: 1,
          radius: 1,
          startTime: 1,
          endTime: 1,
          cannabisLicense: 1,
          resellersPermit: 1,
          availableDays: 1,
          paymentOption: 1,
          distance: {
            $round: ['$distance', 2] // Round to 2 decimal places
          },
          rating: {
            $cond: {
              if: { $gt: [{ $size: '$ratingData' }, 0] },
              then: {
                $round: [
                  { $arrayElemAt: ['$ratingData.averageRating', 0] },
                  1
                ]
              },
              else: null
            }
          },
          totalReviews: {
            $ifNull: [
              { $arrayElemAt: ['$ratingData.totalReviews', 0] },
              0
            ]
          },
          isApproved: 1,
          createdAt: 1,
          updatedAt: 1
        }
      },
      {
        $sort: { distance: 1 } // Sort by distance (nearest first)
      }
    ]);
  } catch (err) {
    console.error('Error fetching nearby providers:', err);
    return sendError(res, 'Failed to find nearby providers', httpStatus.INTERNAL_SERVER_ERROR);
  }

  if (nearbyProviders.length === 0) {
    return sendSuccess(res, 'No approved providers found within 100km radius', httpStatus.OK, []);
  }

  console.log(`Found ${nearbyProviders.length} nearby providers`);

  sendSuccess(res, 'Nearby providers fetched successfully!', httpStatus.OK, nearbyProviders);
});

const getDashboardCounts = catchAsync(async (req, res) => {
  try {
    const [totalCustomers, totalProviders, totalProducts, totalOrders] = await Promise.all([
      User.countDocuments({ role: 'customer' }),
      User.countDocuments({ role: 'provider' }),
      Product.countDocuments(),
      Order.countDocuments(),
    ]);

    const result = {
      totalCustomers,
      totalProviders,
      totalProducts,
      totalOrders,
    };

    sendSuccess(res, 'Dashboard counts fetched successfully', httpStatus.OK, result);
  } catch (error) {
    console.error('Dashboard count error:', error);
    sendError(res, 'Failed to fetch dashboard counts', httpStatus.INTERNAL_SERVER_ERROR);
  }
});


// Get Provider By ID - Simple version
// const getProviderById = catchAsync(async (req, res) => {
//   const providerId = req.params.provider || req.params.providerId;

//   if (!providerId) {
//     return sendError(res, 'Provider ID is required', httpStatus.BAD_REQUEST);
//   }

//   console.log('Looking for provider with ID:', providerId);

//   try {
//     // Simple aggregation to get provider with user details and rating
//     const providerDetails = await GlobalService.getAllByFilter('Provider',
//       { _id: providerId, isApproved: 'approved' },
//       [
//         {
//           $lookup: {
//             from: 'users',
//             localField: 'user',
//             foreignField: '_id',
//             as: 'userDetails',
//             pipeline: [{ $project: { email: 1 } }]
//           }
//         },
//         {
//           $unwind: {
//             path: '$userDetails',
//             preserveNullAndEmptyArrays: true
//           }
//         },
//         {
//           $lookup: {
//             from: 'reviews',
//             let: { providerId: '$_id' },
//             pipeline: [
//               {
//                 $lookup: {
//                   from: 'orders',
//                   localField: 'order_id',
//                   foreignField: '_id',
//                   as: 'order'
//                 }
//               },
//               {
//                 $unwind: '$order'
//               },
//               {
//                 $match: {
//                   $expr: { $eq: ['$order.provider_id', '$$providerId'] }
//                 }
//               },
//               {
//                 $group: {
//                   _id: null,
//                   averageRating: { $avg: '$rating' },
//                   totalReviews: { $sum: 1 }
//                 }
//               }
//             ],
//             as: 'ratingData'
//           }
//         },
//         {
//           $project: {
//             _id: 1,
//             name: 1,
//             description: 1,
//             photoId: 1,
//             email: '$userDetails.email',
//             street: 1,
//             city: 1,
//             state: 1,
//             country: 1,
//             zipCode: 1,
//             latitude: 1,
//             longitude: 1,
//             radius: 1,
//             startTime: 1,
//             endTime: 1,
//             cannabisLicense: 1,
//             resellersPermit: 1,
//             availableDays: 1,
//             paymentOption: 1,
//             rating: {
//               $cond: {
//                 if: { $gt: [{ $size: '$ratingData' }, 0] },
//                 then: {
//                   $round: [
//                     { $arrayElemAt: ['$ratingData.averageRating', 0] },
//                     1
//                   ]
//                 },
//                 else: null
//               }
//             },
//             totalReviews: {
//               $ifNull: [
//                 { $arrayElemAt: ['$ratingData.totalReviews', 0] },
//                 0
//               ]
//             },
//             isApproved: 1,
//             createdAt: 1,
//             updatedAt: 1
//           }
//         }
//       ]
//     );

//     if (!providerDetails || providerDetails.length === 0) {
//       return sendError(res, 'Provider not found', httpStatus.NOT_FOUND);
//     }

//     const provider = providerDetails[0];
//     sendSuccess(res, 'Provider fetched successfully!', httpStatus.OK, provider);
//   } catch (err) {
//     console.error('Error fetching provider:', err);
//     return sendError(res, 'Failed to fetch provider details', httpStatus.INTERNAL_SERVER_ERROR);
//   }
// });





const getProfile = catchAsync(async (req, res) => {
  const userId = req.user._id;

  // Find customer by user ID with aggregation to include user details
  const customers = await GlobalService.getAllByFilter(modelName, { user: userId }, aggregation);

  if (!customers || customers.length === 0) {
    return sendError(res, 'Customer profile not found', httpStatus.NOT_FOUND);
  }

  const customer = customers[0];

  // Return customer profile with username, bio, photo, and email
  const profileData = {
    _id: customer._id,
    username: customer.username,
    bio: customer.bio,
    photo: customer.photo,
    email: customer.email,
    emailPermission: customer.emailPermission,
    notificationPermission: customer.notificationPermission,
    address: customer.address,
    location: customer.location,
    createdAt: customer.createdAt,
    updatedAt: customer.updatedAt
  };

  sendSuccess(res, 'Customer profile fetched successfully!', httpStatus.OK, profileData);
});

module.exports = { create, index, view, update, softDelete, username, updateCustomer, updateCustomerAddress, users, getNearbyProviders, getDashboardCounts, getProfile };